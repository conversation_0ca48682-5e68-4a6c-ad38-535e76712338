
#import "@preview/noteworthy:0.2.0": *

#show: noteworthy.with(
  paper-size: "a4",
  font: ("Times New Roman", "SimSun"),
  language: "ZH",
  title: "Transformer 模型架构详解",
  author: "YDL",
  contact-details: "<EMAIL>",
  toc-title: "目录",
  watermark: "YDL", // 可选：文档水印
)

// 设置段落间距
#set par(
  leading: 0.65em,    // 行间距
  spacing: 1.2em,     // 段落间距
  justify: true,      // 两端对齐
  first-line-indent: 2em, // 首行缩进
)

// 设置标题间距
#show heading: it => {
  set block(spacing: 1.5em)
  it
}

// 正文内容

= 引言

Transformer 架构由 Vaswani 等人于 2017 年提出，它完全依赖注意力机制而摒弃了循环和卷积操作，代表了序列建模的范式转变。这种革命性的方法从根本上改变了自然语言处理领域，并在各个领域都取得了显著的成功。

Transformer 模型由四个主要组件协同工作来处理序列数据：

== 多头注意力机制

多头注意力机制允许模型同时关注来自不同位置的不同表示子空间的信息。与计算单一注意力函数的传统注意力机制不同，多头注意力并行运行多个注意力函数。

该机制的核心思想是将查询（Query）、键（Key）和值（Value）投影到多个不同的表示空间中，然后在每个空间中独立计算注意力，最后将结果连接起来。这种设计使模型能够捕获不同类型的依赖关系。

== 位置编码

由于 Transformer 不包含循环或卷积结构，因此必须注入一些关于序列中标记的相对或绝对位置的信息。位置编码被添加到编码器和解码器堆栈底部的输入嵌入中。

位置编码使用正弦和余弦函数的组合，这种设计允许模型学习相对位置关系，并且可以处理比训练时更长的序列。

== 前馈神经网络

编码器和解码器中的每一层都包含一个全连接的前馈网络，该网络分别且相同地应用于每个位置。它由两个线性变换组成，中间有一个 ReLU 激活函数。

前馈网络的作用是对每个位置的表示进行非线性变换，增强模型的表达能力。

== 层归一化和残差连接

Transformer 在每个子层周围采用残差连接，然后进行层归一化。这有助于梯度流动，并使更深网络的训练成为可能。

残差连接的公式为：$text("LayerNorm")(x + text("Sublayer")(x))$，其中 Sublayer 是子层实现的函数。

= 实现细节

原始的 Transformer 模型使用了特定的超参数，这些参数在许多实现中已成为标准：

- 模型维度 (d_model)：512
- 注意力头数：8
- 前馈网络维度：2048
- 层数：6（编码器和解码器各6层）

这些架构选择反映了模型容量和计算效率之间的精心平衡。

= 数学公式

多头注意力的计算公式如下：

$
text("MultiHead")(Q, K, V) = text("Concat")(text("head")_1, ..., text("head")_h) W^O
$

其中每个注意力头计算为：

$
text("head")_i = text("Attention")(Q W_i^Q, K W_i^K, V W_i^V)
$

缩放点积注意力的公式为：

$
text("Attention")(Q, K, V) = text("softmax")(frac(Q K^T, sqrt(d_k))) V
$

= 结论

Transformer 架构已被证明是极其通用和有效的，它作为自然语言处理、计算机视觉和其他领域中许多最先进模型的基础。它通过自注意力捕获长距离依赖关系的能力使其在现代深度学习应用中变得不可或缺。

该架构的成功催生了 BERT、GPT、T5 等一系列重要模型，推动了人工智能领域的快速发展。


= ICPC / 算法竞赛代码刷题模板 (C++ & Java)

这里是为 ICPC / 算法竞赛准备的 C++ 和 Java 代码模板，包含快速输入输出、常用头文件/包、类型定义和基本结构。
这些模板旨在节省比赛中的设置时间，让你专注于解题逻辑。

*核心思想：*
+ *快速 I/O*: 比赛数据量大时，默认 I/O 可能会超时 (TLE)。
+ *结构化*: 将每个测试用例的逻辑放在 `solve()` 函数中，便于处理多组测试数据。
+ *常用工具*: 预定义常用类型别名、宏、常量和辅助函数。

#line(length: 100%, stroke: 0.5pt + gray)
#v(1em) // 增加垂直间距

// =================================================================
== 1. C++ ICPC Template
包含了常用头文件、快速 I/O、类型别名、宏定义和多测例结构。

```cpp
#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
#include <cmath>
#include <map>
#include <set>
#include <queue>
#include <stack>
#include <numeric>  // std::gcd, std::lcm (C++17), iota, accumulate
#include <iomanip>  // std::setprecision
#include <functional>

// 如果编译器支持，也可以直接用 #include <bits/stdc++.h> 包含大部分头文件
// 但不是所有环境都支持，了解具体头文件更好

using namespace std;

//----------------------------------------------------------------
// 类型别名 Type Aliases
//----------------------------------------------------------------
using ll = long long;
using ull = unsigned long long;
using pii = pair<int, int>;
using pll = pair<ll, ll>;
using vi = vector<int>;
using vl = vector<ll>;
using vvi = vector<vector<int>>;
using vvl = vector<vector<ll>>;

//----------------------------------------------------------------
// 宏定义 Macros
//----------------------------------------------------------------
#define REP(i, n) for (int i = 0; i < (n); ++i)
#define FOR(i, a, b) for (int i = (a); i <= (b); ++i)
#define FORD(i, a, b) for (int i = (a); i >= (b); --i)
#define all(x) (x).begin(), (x).end()
#define sz(x) (int)(x).size()
#define pb push_back
#define mp make_pair
#define fi first
#define se second

// 调试宏 (只在本地编译时有效, 提交时定义 NDEBUG 或删除 LOCAL)
#ifdef LOCAL
#define dbg(x) cerr << #x << " = " << (x) << endl
#else
#define dbg(x)
#endif

//----------------------------------------------------------------
// 常量 Constants
//----------------------------------------------------------------
const ll MOD = 1e9 + 7; // 或 998244353
const ll INF_LL = 1e18;
const int INF_INT = 1e9;
const double PI = acos(-1.0);
const double EPS = 1e-9; // 浮点数比较精度

//----------------------------------------------------------------
// 辅助函数 Helpers
//----------------------------------------------------------------
// 快速幂 (base^exp % mod)
ll power(ll base, ll exp) {
    ll res = 1;
    base %= MOD;
    while (exp > 0) {
        if (exp % 2 == 1) res = (res * base) % MOD;
        base = (base * base) % MOD;
        exp /= 2;
    }
    return res;
}

// 最大公约数 (C++17 可以用 std::gcd)
ll gcd(ll a, ll b) {
     return b == 0 ? a : gcd(b, a % b);
}

// 最小公倍数 (C++17 可以用 std::lcm)
 ll lcm(ll a, ll b) {
      if (a == 0 || b == 0) return 0;
       // 先除后乘防溢出
      return abs(a / gcd(a,b) * b) ;
 }

// 设置浮点数输出精度
void set_precision(int p) {
     cout << fixed << setprecision(p);
}

//----------------------------------------------------------------
// 核心逻辑 Solve Function
//----------------------------------------------------------------
void solve() {
    // --- 在这里写单个测试用例的逻辑 ---
    int n;
    cin >> n;
    vl a(n);
    ll sum = 0;
     REP(i, n) {
       cin >> a[i];
       sum += a[i];
       dbg(a[i]); // 调试
     }
    // 注意：循环中用'\n'代替endl，避免频繁刷新缓冲区
    cout << "sum is: " << sum << "\n"; 
     // set_precision(10); // 如果需要输出浮点数
    // --- 结束 ---
}

//----------------------------------------------------------------
// 主函数 Main
//----------------------------------------------------------------
int main() {
    // 快速 I/O
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);
    cout.tie(NULL);

    int t = 1;
    cin >> t;  // ⭐⭐ 如果题目是单个测试用例，把这行注释掉 ⭐⭐
    // int case_num = 1;
    while (t--) {
       // cout << "Case #" << case_num++ << ": "; // Google Code Jam 风格
        solve();
    }
    // solve(); // ⭐⭐ 如果题目是单个测试用例，取消这行注释并注释掉while循环 ⭐⭐

    return 0;
}
```

=== C++ 注意事项:

`ios_base::sync_with_stdio(false); cin.tie(NULL);` 是必须的加速。
在循环或大量输出时，使用 `'\n'` 比 `std::endl` 快，因为 endl 会强制刷新缓冲区 (flush)。
注意 int 和 long long 的范围，防止溢出。1e9 左右用 int, 更大的用 long long。
`#include <bits/stdc++.h>` 很方便，但不是标准库，某些严格的 OJ 或老旧编译器可能不支持。
`dbg(x)` 宏配合 `#ifdef LOCAL` 可以在本地调试输出变量，提交时自动失效，非常方便。你需要在本地编译时加上 -DLOCAL 参数 (如 g++ test.cpp -o test -DLOCAL)，或者直接在代码最前面 define LOCAL (提交时注释掉)。
多测例 (Multiple Test Cases)：注意模板里的 cin >> t 和 while(t--) 循环，以及针对单测例情况的注释说明。
#pagebreak() // 分页

// =================================================================
== 2. Java ICPC Template
Java 的 Scanner 和 System.out.println 较慢，模板中自定义了 FastReader 类并使用 PrintWriter 进行加速。

```java
import java.io.*;
import java.util.*;
import java.math.BigInteger; // 需要高精度时使用

// 提交时类名通常需要是 Main
public class Main {

    //----------------------------------------------------------------
    // 全局变量和常量 Global Vars & Constants
    //----------------------------------------------------------------
    static FastReader in;
    static PrintWriter out;
    static final long MOD = (long) 1e9 + 7; // 或 998244353L
    static final long INF_L = (long) 1e18;
    static final int INF_I = (int) 1e9;
    static final double EPS = 1e-9;

    //----------------------------------------------------------------
    // 核心逻辑 Solve Function
    //----------------------------------------------------------------
     // 如果方法内调用了可能抛出IOException的函数（如nextLine），需要 throws IOException
    public static void solve() throws IOException {
         // --- 在这里写单个测试用例的逻辑 ---
         int n = in.nextInt();
         long[] a = new long[n];
         long sum = 0;
         for(int i=0; i<n; ++i){
            a[i] = in.nextLong();
            sum += a[i];
           // System.err.println("Debug: a["+i+"] = " + a[i]); // 调试输出到 stderr
         }
        // 读取数组的快捷方法
        // int[] arr = in.nextIntArray(n);
        // long[] arrL = in.nextLongArray(n);

         out.println("sum is: " + sum);
         // out.printf("%.10f\n", someDoubleValue); // 格式化输出浮点数
         // --- 结束 ---
    }

    //----------------------------------------------------------------
    // 主函数 Main
    //----------------------------------------------------------------
    public static void main(String[] args) throws IOException {
        // 本地调试可以重定向到文件: 
        // in = new FastReader(new FileInputStream("input.txt"));
        // out = new PrintWriter(new BufferedWriter(new FileWriter("output.txt")));
        in = new FastReader(System.in);
        // 使用 BufferedWriter 包装 PrintWriter 提高效率
        out = new PrintWriter(new BufferedWriter(new OutputStreamWriter(System.out)));

        int t = 1;
         t = in.nextInt(); // ⭐⭐ 如果题目是单个测试用例，把这行注释掉 ⭐⭐
        // int case_num = 1;
        while (t-- > 0) {
           // out.print("Case #" + (case_num++) + ": "); // Google Code Jam 风格
            solve();
        }
        // solve(); // ⭐⭐ 如果题目是单个测试用例，取消这行注释并注释掉while循环 ⭐⭐

        out.close(); // ⭐⭐ 非常重要! 确保所有缓冲输出都被写入 ⭐⭐
    }
    
     //----------------------------------------------------------------
     // 辅助函数 Helpers
     //----------------------------------------------------------------
      // 快速幂 (base^exp % mod)
      static long power(long base, long exp) {
          long res=1;
          base %= MOD;
          while(exp>0){
             if(exp % 2 == 1) res = (res * base) % MOD;
             base = (base * base) % MOD;
             exp /= 2;
          }
          return res;
       }
      
       static long gcd(long a, long b) {
           return b == 0 ? a : gcd(b, a % b);
       }
       static long lcm(long a, long b) {
            if (a == 0 || b == 0) return 0;
            // 先除后乘，防止中间结果溢出
             return Math.abs(a / gcd(a, b) * b) ;
        }

    //----------------------------------------------------------------
    // 快速输入类 FastReader
    //----------------------------------------------------------------
    static class FastReader {
        BufferedReader br;
        StringTokenizer st;

        public FastReader(InputStream inputStream) {
            br = new BufferedReader(new InputStreamReader(inputStream));
        }

        String next() {
            while (st == null || !st.hasMoreElements()) {
                try {
                   String line = br.readLine();
                   if(line == null) return null; // Handle EOF
                    st = new StringTokenizer(line);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return st.nextToken();
        }

        int nextInt() { return Integer.parseInt(next()); }
        long nextLong() { return Long.parseLong(next()); }
        double nextDouble() { return Double.parseDouble(next()); }
        
        // 读取整行，注意与next()混合使用时清空tokenizer
        String nextLine() {
            String str = "";
             // 如果当前行还有token，读取当前行剩余部分
            if (st != null && st.hasMoreTokens()) {
                 str = st.nextToken("\n");
             } else {
                 try {
                     str = br.readLine();
                 } catch (IOException e) {
                     e.printStackTrace();
                 }
            }
            return str;
        }
         
        int[] nextIntArray(int n) {
            int[] a = new int[n];
            for (int i = 0; i < n; i++) a[i] = nextInt();
            return a;
        }

        long[] nextLongArray(int n) {
            long[] a = new long[n];
            for (int i = 0; i < n; i++) a[i] = nextLong();
            return a;
        }
    }
     
   //----------------------------------------------------------------
   // 自定义Pair类 (如果需要, 且需要排序/放入Set/Map)
   //----------------------------------------------------------------
   /*
    static class Pair implements Comparable<Pair>{
        int first;
        int second;
        public Pair(int first, int second){
            this.first = first;
            this.second = second;
        }
         @Override
        public int compareTo(Pair other) {
             // Example: sort by first, then by second
            if (this.first != other.first) {
               return Integer.compare(this.first, other.first);
            }
            return Integer.compare(this.second, other.second);
        }
        // 如果要放入 HashSet/HashMap, 必须重写 hashCode 和 equals
        @Override
        public boolean equals(Object o) {
           if (this == o) return true;
           if (o == null || getClass() != o.getClass()) return false;
           Pair pair = (Pair) o;
           return first == pair.first && second == pair.second;
        }
       @Override
       public int hashCode() {
          return Objects.hash(first, second);
       }
       @Override
        public String toString() {
            return "(" + first + ", " + second + ")";
        }
    }
    */
}
```

=== Java 注意事项:

`out.close()` : 必须在 main 函数的最后调用，否则 PrintWriter/BufferedWriter 缓冲区的内容可能不会被完全输出！ out.flush() 也可以，但 close 更彻底。
FastReader: 使用 BufferedReader 和 StringTokenizer 比 Scanner 快得多。
PrintWriter: 使用 PrintWriter (最好用 BufferedWriter 包装一下) 比 System.out.println 快。
throws IOException: 因为 I/O 操作可能抛出异常，main 和 solve 方法签名需要加上 throws IOException。
static: 因为从静态方法 main 调用 solve 和辅助函数，以及访问 in, out，所以它们都需要声明为 static。 FastReader 类也声明为 static class。
数据类型: 注意 int 和 long。常量 1e9 + 7 需要强制转换为 (long)1e9 + 7 或直接写 1000000007L。
集合与基本类型: ArrayList<Integer> 使用包装类，性能略低于 int[]。如果数组大小固定且操作简单，优先使用基本类型数组 int[], long[]。
调试: 可以使用 System.err.println() 进行调试输出，它输出到标准错误流，通常不会被 OJ 的输出检查捕获（但不是 100% 保证，最好提交前注释掉）。
Pair 类: Java 标准库没有方便通用的 Pair 类，如果频繁使用且需要排序或放入集合，需要自己写一个（模板中注释了示例），或者用 int[]{x, y}， Map.Entry 等替代。
多测例 (Multiple Test Cases)：注意模板里的 in.nextInt() 和 while(t-- > 0) 循环，以及针对单测例情况的注释说明。
nextLine() 与 next() / nextInt() 等混用时需小心，next() 只读取一个 token，换行符可能留在缓冲区，影响后续 nextLine() 的读取。模板中的 nextLine 做了一定处理，但复杂情况仍需留意。
#line(length: 100%, stroke: 0.5pt + gray)
#v(1em)

// =================================================================
== 3. 如何使用 (How to Use):

比赛/刷题开始时，复制对应语言的模板。
根据题目是单个测试用例还是多个测试用例，注释/取消注释 main 函数中读取 t 和 while 循环 / 单个 solve() 调用的部分（模板中有 ⭐⭐ 标记）。
在 solve() 函数中编写解题逻辑。
根据需要添加/删除头文件/包、常量、辅助函数。
注意数据范围，选择 int / long long (C++) 或 int / long (Java)。
注意取模运算的 MOD 常量，根据题目要求修改。