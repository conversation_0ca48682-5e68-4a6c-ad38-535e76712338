// 高级 Typst 功能测试文档
// 注意：某些包可能需要根据 Typst 版本调整
#import "@preview/physica:0.9.5"
// SCI论文级别页面设置
#set page(
  paper: "a4",
  margin: (left: 2.5cm, right: 2.5cm, top: 2.5cm, bottom: 2.5cm),
  header: context [
    #set text(9pt, fill: rgb("#666666"))
    #if counter(page).get().first() > 1 [
      #smallcaps[Transformer Architecture Analysis] #h(1fr) #counter(page).display()
    ]
  ],
  footer: context [
    #set text(8pt, fill: rgb("#666666"))
    #align(center)[
      #datetime.today().display("[month repr:short] [day], [year]")
    ]
  ]
)

// SCI论文字体和格式设置
#set text(
  font: ("Times New Roman", "SimSun"),
  size: 12pt,
  lang: "zh"
)
#set par(
  justify: true,
  leading: 0.6em,
  first-line-indent: 2em
)
#set heading(numbering: "1.1")

// 引用和参考文献样式
#show cite: it => text(fill: rgb("#0066CC"))[#it]

// SCI论文级别配色方案
#let primary-color = rgb("#000000")      // 纯黑色 - 主文字
#let secondary-color = rgb("#333333")    // 深灰色 - 次要文字
#let accent-color = rgb("#0066CC")       // 学术蓝 - 链接和强调
#let light-gray = rgb("#F8F9FA")         // 极浅灰背景
#let medium-gray = rgb("#E9ECEF")        // 浅灰边框
#let text-dark = rgb("#000000")          // 黑色文字
#let text-muted = rgb("#6C757D")         // 灰色辅助文字

// SCI论文标题样式
#show heading.where(level: 1): it => [
  #set text(16pt, weight: "bold", fill: primary-color)
  #block(above: 18pt, below: 12pt)[
    #upper(it.body)
  ]
]

#show heading.where(level: 2): it => [
  #set text(14pt, weight: "bold", fill: primary-color)
  #block(above: 14pt, below: 8pt)[
    #it.body
  ]
]

#show heading.where(level: 3): it => [
  #set text(12pt, weight: "bold", fill: primary-color)
  #block(above: 12pt, below: 6pt)[
    #it.body
  ]
]

#show heading.where(level: 4): it => [
  #set text(12pt, weight: "bold", fill: primary-color, style: "italic")
  #block(above: 10pt, below: 4pt)[
    #it.body
  ]
]

// SCI论文级别组件
#let abstract-box(content) = [
  #block(
    width: 100%,
    inset: (x: 1.5em, y: 1em)
  )[
    #set text(size: 11pt)
    #set par(justify: true, leading: 0.65em)
    #align(center)[
      #text(weight: "bold", size: 12pt)[ABSTRACT]
    ]
    #v(0.5em)
    #content
  ]
]

#let keywords-box(keywords) = [
  #block(
    width: 100%,
    inset: (x: 1.5em, y: 0.5em)
  )[
    #set text(size: 11pt)
    #text(weight: "bold")[Keywords: ] #keywords
  ]
]

#let figure-caption(content, label: none) = [
  #set text(size: 10pt)
  #set par(justify: true)
  #text(weight: "bold")[Figure. ] #content
  #if label != none [
    #label
  ]
]

#let table-caption(content, label: none) = [
  #set text(size: 10pt)
  #set par(justify: true)
  #text(weight: "bold")[Table. ] #content
  #if label != none [
    #label
  ]
]

#let equation-block(content, label: none) = [
  #block(
    width: 100%,
    inset: (y: 0.5em)
  )[
    #align(center)[
      #content
      #if label != none [
        #h(1fr) #label
      ]
    ]
  ]
]

// SCI论文定理环境
#let theorem(title, content) = [
  #block(
    width: 100%,
    inset: (x: 1em, y: 0.8em),
    stroke: (left: 2pt + accent-color)
  )[
    #text(weight: "bold", size: 12pt, style: "italic")[Theorem #title.]
    #v(0.3em)
    #set text(style: "italic")
    #content
  ]
]

#let definition(title, content) = [
  #block(
    width: 100%,
    inset: (x: 1em, y: 0.8em),
    stroke: (left: 2pt + secondary-color)
  )[
    #text(weight: "bold", size: 12pt)[Definition #title.]
    #v(0.3em)
    #content
  ]
]

#let remark(content) = [
  #block(
    width: 100%,
    inset: (x: 1em, y: 0.5em)
  )[
    #set text(size: 11pt, style: "italic")
    #text(weight: "bold")[Remark.] #content
  ]
]

// SCI论文信息框
#let info-box(title, content) = [
  #block(
    width: 100%,
    inset: (x: 1em, y: 0.8em),
    stroke: (left: 2pt + accent-color)
  )[
    #text(weight: "bold", size: 11pt)[#title]
    #v(0.3em)
    #content
  ]
]

// SCI论文注释框
#let note-box(content) = [
  #remark(content)
]

// SCI论文标准标题页
#align(center)[
  #v(2em)

  // 论文标题 - SCI格式
  #text(18pt, weight: "bold", fill: primary-color)[
    Transformer Architecture: A Comprehensive Analysis of \
    Attention-Based Sequence-to-Sequence Models
  ]

  #v(1.5em)

  // 作者信息 - SCI格式
  #text(12pt)[
    AI Research Team#super[1], Deep Learning Laboratory#super[2]
  ]

  #v(0.8em)

  // 机构信息
  #text(10pt, style: "italic")[
    #super[1]Institute of Artificial Intelligence, Technology University \
    #super[2]Department of Computer Science, Research Institute
  ]

  #v(1em)

  // 通讯作者
  #text(9pt)[
    *Corresponding author:* ai-research\@tech-university.edu
  ]
]

#v(2em)

// 摘要 - SCI标准格式
#abstract-box[
This paper presents a comprehensive analysis of the Transformer architecture, a revolutionary sequence-to-sequence model that relies entirely on attention mechanisms. We provide detailed mathematical derivations of the core components including multi-head attention, positional encoding, and the encoder-decoder framework. The analysis covers the theoretical foundations, implementation details, and practical applications of Transformer models in natural language processing and computer vision tasks. Our work contributes to the understanding of attention-based architectures and their impact on modern deep learning.
]

// 关键词
#keywords-box[
Transformer, attention mechanism, sequence-to-sequence, encoder-decoder, deep learning, neural networks
]

#v(1em)
#line(length: 100%, stroke: 0.5pt + text-muted)
#v(1em)

#pagebreak()

= INTRODUCTION

The Transformer architecture, introduced by Vaswani et al. (2017), represents a paradigm shift in sequence modeling by relying entirely on attention mechanisms while dispensing with recurrence and convolutions. This revolutionary approach has fundamentally transformed natural language processing and has shown remarkable success across various domains.

The Transformer model consists of four primary components that work in concert to process sequential data:

#info-box("Core Architecture Components", [
- *Input Layer*: Token embeddings combined with positional encodings
- *Encoder Stack*: Multiple encoder layers for input sequence processing
- *Decoder Stack*: Multiple decoder layers for output sequence generation
- *Output Layer*: Linear transformation and softmax for probability distribution
])

= METHODOLOGY

== Input Embeddings and Positional Encoding

Input tokens are mapped to $d_text("model")$-dimensional vectors, with positional encodings representing token positions:

#equation-block[
$P E(p o s, 2 i) = sin(frac(p o s, 10000^(frac(2 i, d_text("model"))))), quad P E(p o s, 2 i+1) = cos(frac(p o s, 10000^(frac(2 i, d_text("model")))))$
]

== Attention Mechanisms

=== Scaled Dot-Product Attention

The fundamental attention operation takes queries $Q$, keys $K$, and values $V$ as inputs:

#equation-block[
$text("Attention")(Q, K, V) = text("softmax")(frac(Q K^T, sqrt(d_k))) V$
]

where $d_k$ represents the dimension of queries and keys, and $sqrt(d_k)$ provides scaling to prevent extremely small gradients.

=== Multi-Head Attention

The multi-head mechanism projects $Q$, $K$, and $V$ into multiple representation subspaces:

#equation-block[
$text("MultiHead")(Q, K, V) = text("Concat")(text("head")_1, text("head")_2, dots, text("head")_h) W^O$
]

#equation-block[
$text("head")_i = text("Attention")(Q W_i^Q, K W_i^K, V W_i^V)$
]

== Encoder Architecture

Each encoder layer implements a standardized architecture comprising the following sublayers:

1. *Multi-Head Self-Attention*: Enables the model to attend to different positions within the input sequence
2. *Residual Connection and Layer Normalization*: $x + text("Sublayer")(x)$ where Sublayer represents the attention operation
3. *Position-wise Feed-Forward Network*:
   #equation-block[
   $text("FFN")(x) = max(0, x W_1 + b_1) W_2 + b_2$
   ]
4. *Second Residual Connection and Layer Normalization*: Applied to the FFN output

== Decoder Architecture

#remark[
The decoder architecture mirrors the encoder but incorporates an additional *Masked Multi-Head Self-Attention* layer to prevent information leakage from future positions. This masking mechanism ensures that predictions for position $i$ can only depend on known outputs at positions less than $i$.
]

== Output Layer

The decoder output undergoes linear transformation followed by softmax normalization:

#equation-block[
$P(y_i | y_(< i), x) = text("softmax")(W_text("out") dot h_text("dec"))$
]

= TRAINING METHODOLOGY

The model optimization employs cross-entropy loss:

#equation-block[
$cal(L) = -sum_(i=1)^T log P(y_i | y_(< i), x)$
]

= IMPLEMENTATION

The following pseudocode demonstrates the core computational components of the Transformer architecture:

```python
def scaled_dot_product_attention(Q, K, V, mask=None):
    """
    Compute scaled dot-product attention.

    Args:
        Q: Query matrix of shape (batch_size, seq_len, d_k)
        K: Key matrix of shape (batch_size, seq_len, d_k)
        V: Value matrix of shape (batch_size, seq_len, d_v)
        mask: Optional attention mask

    Returns:
        Attention output and weights
    """
    d_k = Q.shape[-1]
    scores = matmul(Q, K.transpose(-2, -1)) / sqrt(d_k)
    if mask is not None:
        scores = scores.masked_fill(mask == 0, -1e9)
    attention_weights = softmax(scores, dim=-1)
    return matmul(attention_weights, V)

def multi_head_attention(Q, K, V, num_heads):
    """
    Multi-head attention mechanism.

    Args:
        Q, K, V: Input matrices
        num_heads: Number of attention heads

    Returns:
        Multi-head attention output
    """
    batch_size, seq_len, d_model = Q.shape
    Q = Q.reshape(batch_size, seq_len, num_heads, d_model // num_heads)
    K = K.reshape(batch_size, seq_len, num_heads, d_model // num_heads)
    V = V.reshape(batch_size, seq_len, num_heads, d_model // num_heads)
    heads = scaled_dot_product_attention(Q, K, V)
    heads = heads.reshape(batch_size, seq_len, d_model)
    return linear(heads)
```

= CONCLUSIONS

#theorem("1", [
The Transformer architecture achieves superior performance in sequence modeling through pure attention mechanisms, offering significant advantages over traditional RNN/LSTM architectures:

1. *Parallelization*: All positions can be processed simultaneously
2. *Long-range Dependencies*: Direct modeling of relationships between arbitrary positions
3. *Interpretability*: Attention weights provide visualization of model decisions
4. *Scalability*: Efficient scaling to larger models and datasets
])

#info-box("Applications and Impact", [
The Transformer architecture has demonstrated remarkable versatility across multiple domains:
- *Natural Language Processing*: Machine translation, text generation, question answering
- *Computer Vision*: Vision Transformer (ViT), image classification, object detection
- *Multimodal Learning*: Text-image understanding, speech recognition, video analysis
- *Scientific Computing*: Protein structure prediction, drug discovery, molecular modeling
])

The widespread adoption of Transformer-based models has fundamentally reshaped the landscape of artificial intelligence, establishing attention mechanisms as a cornerstone of modern deep learning architectures.

= ACKNOWLEDGMENTS

The authors acknowledge the foundational work of Vaswani et al. in introducing the Transformer architecture and the broader research community for advancing attention-based models.

= REFERENCES

#set text(size: 10pt)
#set par(hanging-indent: 1em)

1. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A. N., ... & Polosukhin, I. (2017). Attention is all you need. *Advances in Neural Information Processing Systems*, 30.

2. Devlin, J., Chang, M. W., Lee, K., & Toutanova, K. (2018). BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding. *arXiv preprint arXiv:1810.04805*.

3. Brown, T., Mann, B., Ryder, N., Subbiah, M., Kaplan, J. D., Dhariwal, P., ... & Amodei, D. (2020). Language models are few-shot learners. *Advances in Neural Information Processing Systems*, 33, 1877-1901.

4. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., ... & Houlsby, N. (2020). An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*.

5. Radford, A., Wu, J., Child, R., Luan, D., Amodei, D., & Sutskever, I. (2019). Language models are unsupervised multitask learners. *OpenAI blog*, 1(8), 9.

6. Liu, Y., Ott, M., Goyal, N., Du, J., Joshi, M., Chen, D., ... & Stoyanov, V. (2019). RoBERTa: A robustly optimized BERT pretraining approach. *arXiv preprint arXiv:1907.11692*.

7. Raffel, C., Shazeer, N., Roberts, A., Lee, K., Narang, S., Matena, M., ... & Liu, P. J. (2020). Exploring the limits of transfer learning with a unified text-to-text transformer. *Journal of Machine Learning Research*, 21(140), 1-67.

8. Shaw, P., Uszkoreit, J., & Vaswani, A. (2018). Self-attention with relative position representations. *Proceedings of the 2018 Conference of the North American Chapter of the Association for Computational Linguistics*, 464-468.

9. Devlin, J., Chang, M. W., Lee, K., & Toutanova, K. (2018). BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding. *arXiv preprint arXiv:1810.04805*.