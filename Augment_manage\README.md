# 放在前面的
此python全程使用vscode+augment编写，所以比较💩，不要在意这些细节问题。

# 团队管理工具使用文档

## 📋 目录
- [概述](#概述)
- [系统要求](#系统要求)
- [安装与启动](#安装与启动)
- [界面介绍](#界面介绍)
- [功能详解](#功能详解)
- [配置设置](#配置设置)
- [常见问题](#常见问题)
- [技术支持](#技术支持)

## 🚀 概述

团队管理工具是一款基于PyQt6开发的现代化团队成员管理应用程序，专为Augment Code平台设计。该工具提供了直观的图形界面，支持批量邀请成员、查看团队信息、管理邀请记录等功能。

### 主要特性
- 🎨 **现代化UI设计** - 采用Material Design风格，界面美观易用
- 📧 **批量邀请功能** - 支持一次性邀请多个成员，自动验证邮箱格式
- 📊 **实时数据统计** - 提供团队成员、邀请状态等实时统计信息
- 🔧 **灵活配置管理** - 支持API配置、界面设置、功能开关等多项配置
- 📋 **详细日志系统** - 记录所有操作日志，支持分级显示和导出
- 🔄 **自动刷新机制** - 支持定时自动刷新团队数据
- 💾 **数据导出功能** - 支持导出邀请列表、配置文件等

## 💻 系统要求

### 最低配置
- **操作系统**: Windows 10/11, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Python版本**: Python 3.8 或更高版本
- **内存**: 4GB RAM
- **存储空间**: 100MB 可用空间
- **网络**: 稳定的互联网连接

### 依赖库
```bash
PyQt6>=6.0.0
requests>=2.25.0
```

## 🛠️ 安装与启动

### 方法一：直接运行Python脚本
```bash
# 1. 确保已安装Python 3.8+
python --version

# 2. 安装依赖库
pip install PyQt6 requests

# 3. 运行程序
python team_manager.py
```

### 方法二：使用虚拟环境（推荐）
```bash
# 1. 创建虚拟环境
python -m venv team_manager_env

# 2. 激活虚拟环境
# Windows:
team_manager_env\Scripts\activate
# macOS/Linux:
source team_manager_env/bin/activate

# 3. 安装依赖
pip install PyQt6 requests

# 4. 运行程序
python team_manager.py
```

### 启动成功标志
程序启动后会显示：
- 🎉 团队管理工具启动成功！
- 💡 提示：请在 工具 -> 配置设置 中配置API信息

## 🖥️ 界面介绍

### 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│  🚀 团队管理工具                    [连接状态] [刷新按钮]  │
├─────────────────────────────────────────────────────────┤
│  [邀请成员] [团队管理] [批量操作] [计划管理] [系统日志]   │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    主要内容区域                          │
│                                                         │
├─────────────────────────────────────────────────────────┤
│  状态栏: [操作状态] [进度条] [连接状态]                   │
└─────────────────────────────────────────────────────────┘
```

### 标签页说明
1. **邀请成员** - 批量邀请新成员加入团队
2. **团队管理** - 查看和管理现有团队成员
3. **批量操作** - 执行批量删除等操作
4. **计划管理** - 管理用户订阅计划
5. **系统日志** - 查看操作日志和系统状态

## ⚙️ 功能详解

### 1. 邀请成员功能

#### 使用步骤
1. 点击"邀请成员"标签页
2. 在邮箱输入框中输入邮箱地址（每行一个）
3. 系统会实时验证邮箱格式并显示统计信息
4. 点击"发送邀请"按钮执行邀请操作

#### 邮箱格式要求
- 每行输入一个邮箱地址
- 支持标准邮箱格式：`<EMAIL>`
- 自动过滤重复和无效邮箱

#### 实时统计显示
- 📊 **总邮箱数** - 输入的邮箱总数量
- ✅ **有效邮箱** - 格式正确的邮箱数量  
- ❌ **无效邮箱** - 格式错误的邮箱数量

#### 辅助功能
- **从剪贴板粘贴** - 快速导入邮箱列表
- **清空输入** - 清除所有输入内容
- **邀请历史** - 查看历史邀请记录

### 2. 团队管理功能

#### 数据获取
- 点击"🔄 获取团队数据"按钮获取最新团队信息
- 支持自动刷新，可在配置中设置刷新间隔

#### 成员信息显示
团队成员表格包含以下信息：
- **序号** - 成员编号
- **ID** - 用户唯一标识
- **邮箱** - 成员邮箱地址
- **角色** - 成员在团队中的角色
- **加入时间** - 成员加入团队的时间

#### 邀请记录显示
邀请记录表格包含：
- **序号** - 邀请编号
- **ID** - 邀请记录ID
- **邮箱** - 被邀请人邮箱
- **邀请时间** - 发送邀请的时间

#### 统计信息
- 👥 **总成员数** - 团队总人数
- ✅ **活跃成员** - 已加入的成员数
- ⏳ **待加入人数** - 未接受邀请的人数
- 📧 **总邀请数** - 发送的邀请总数
- 📅 **今日邀请** - 今天发送的邀请数

### 3. 批量操作功能

#### 支持的批量操作
- **批量删除未加入成员** - 删除所有未接受邀请的记录
- **批量删除邀请记录** - 删除所有邀请记录
- **批量删除全部** - 删除所有成员和邀请记录

#### 安全确认机制
- 所有批量操作都需要用户确认
- 显示详细的操作影响说明
- 支持操作撤销（在执行前）

#### 操作日志
- 记录所有批量操作的详细信息
- 包含操作时间、影响范围、执行结果
- 支持日志导出和查看

### 4. 计划管理功能

#### 支持的计划类型
- **Community Plan** - 社区版计划
- **Max Plan** - 专业版计划

#### 计划切换
- 一键切换用户订阅计划
- 实时显示切换结果
- 自动更新用户状态

### 5. 系统日志功能

#### 日志级别
- **信息** - 一般操作信息
- **成功** - 操作成功提示
- **警告** - 需要注意的情况
- **错误** - 操作失败或异常

#### 日志管理
- **级别过滤** - 按日志级别筛选显示
- **自动滚动** - 新日志自动滚动到底部
- **清空日志** - 清除所有日志记录
- **导出日志** - 将日志保存为文本文件

## 🔧 配置设置

### 打开配置界面
菜单栏 → 工具 → 配置设置

### API配置
这是最重要的配置部分，必须正确配置才能使用所有功能。

#### 基础配置
- **API基础URL**: `https://app.augmentcode.com/api`
- **Cookie**: 从浏览器开发者工具获取的完整Cookie字符串

#### Cookie获取方法
1. 在浏览器中登录Augment Code平台
2. 按F12打开开发者工具
3. 切换到"Network"标签页
4. 刷新页面或执行任何操作
5. 找到任意一个请求，查看Request Headers
6. 复制完整的Cookie值粘贴到配置中

#### 其他请求头配置
- **User Agent**: 浏览器标识信息
- **Referer**: 来源页面URL
- **Accept**: 接受的内容类型
- **Accept-Language**: 接受的语言

### 界面配置

#### 外观设置
- **界面主题**: light/dark/auto
- **字体大小**: 8-24px
- **窗口透明度**: 70-100%

#### 行为设置
- **启用自动刷新**: 是否自动刷新数据
- **刷新间隔**: 10-600秒
- **启动时最小化**: 程序启动时是否最小化
- **关闭到系统托盘**: 关闭时是否隐藏到托盘

#### 显示设置
- **启用动画效果**: 界面动画开关
- **显示阴影效果**: 组件阴影开关
- **表格最大行数**: 50-1000行

### 功能配置

#### 核心功能
- **启用批量操作**: 批量操作功能开关
- **邮箱格式验证**: 邮箱验证功能开关
- **自动保存配置**: 配置自动保存开关
- **启用数据导出**: 数据导出功能开关

#### 安全设置
- **危险操作确认**: 是否需要确认危险操作
- **启用操作日志**: 操作日志记录开关
- **自动备份配置**: 配置文件自动备份

#### 性能设置
- **请求超时时间**: 5-120秒
- **请求重试次数**: 0-10次
- **并发请求限制**: 1-20个

### 高级配置

#### 调试设置
- **启用调试模式**: 调试信息输出开关
- **详细日志输出**: 详细日志记录开关
- **保存请求日志**: HTTP请求日志保存

#### 网络设置
- **启用代理**: 代理服务器开关
- **代理主机**: 代理服务器地址
- **代理端口**: 代理服务器端口
- **SSL证书验证**: SSL证书验证开关

#### 数据管理
- **配置文件路径**: 显示当前配置文件位置
- **导出配置**: 将当前配置导出为文件
- **导入配置**: 从文件导入配置
- **清理缓存**: 清除应用缓存数据

### 配置测试
配置完成后，点击"🧪 测试连接"按钮验证配置是否正确：
- ✅ 连接成功：显示团队信息摘要
- ❌ 连接失败：显示错误原因和解决建议

## ❓ 常见问题

### Q1: 程序启动失败怎么办？
**A**: 检查以下几点：
1. 确认Python版本是否为3.8+
2. 确认已安装PyQt6和requests库
3. 检查是否有其他程序占用相关端口
4. 查看控制台错误信息

### Q2: API连接失败怎么办？
**A**: 按以下步骤排查：
1. 检查网络连接是否正常
2. 确认API URL是否正确
3. 重新获取并配置Cookie
4. 检查Cookie是否包含必要的认证信息
5. 尝试使用代理或VPN

### Q3: Cookie过期怎么办？
**A**: Cookie有时效性，需要定期更新：
1. 重新登录Augment Code平台
2. 按照Cookie获取方法重新获取
3. 在配置设置中更新Cookie
4. 点击测试连接验证

### Q4: 邀请发送失败怎么办？
**A**: 检查以下方面：
1. 确认邮箱格式是否正确
2. 检查API连接状态
3. 确认账号是否有邀请权限
4. 查看系统日志了解具体错误

### Q5: 数据显示不完整怎么办？
**A**: 可能的解决方案：
1. 点击刷新按钮重新获取数据
2. 检查表格行数限制设置
3. 确认API返回数据是否完整
4. 重启程序重新加载

### Q6: 程序运行缓慢怎么办？
**A**: 优化建议：
1. 减少表格最大行数限制
2. 关闭不必要的动画效果
3. 增加请求超时时间
4. 减少自动刷新频率

## 📞 技术支持

### 日志文件位置
- **配置文件**: `team_manager_config.json`
- **日志文件**: 可通过"导出日志"功能生成

### 错误报告
如遇到问题，请提供以下信息：
1. 操作系统版本
2. Python版本
3. 错误截图或日志
4. 复现步骤
5. 配置信息（隐藏敏感数据）

### 版本信息
- **当前版本**: v3.0 Pro
- **技术栈**: Python 3.8+, PyQt6, Requests
- **开发框架**: Material Design

---

*本文档最后更新时间：2025年6月16日*


