import React from 'react'
import { motion } from 'framer-motion'
import { 
  Cpu, 
  Database, 
  Wifi, 
  BarChart3, 
  Cog, 
  Shield,
  Cloud,
  Smartphone,
  ArrowRight
} from 'lucide-react'

const Services = () => {
  const services = [
    {
      icon: Cpu,
      title: '智能制造系统',
      description: '基于AI和机器学习的智能生产线，实现自动化决策和优化生产流程',
      features: ['生产计划优化', '质量预测分析', '设备智能维护', '能耗优化管理'],
      color: 'from-cyan-500 to-blue-600'
    },
    {
      icon: Wifi,
      title: '工业物联网',
      description: '构建全面的IoT生态系统，连接设备、传感器和系统，实现数据驱动决策',
      features: ['设备互联', '实时监控', '远程控制', '数据采集'],
      color: 'from-purple-500 to-pink-600'
    },
    {
      icon: Database,
      title: '大数据分析',
      description: '深度挖掘生产数据价值，提供精准的业务洞察和预测分析',
      features: ['数据可视化', '预测性维护', '趋势分析', '异常检测'],
      color: 'from-green-500 to-teal-600'
    },
    {
      icon: Cloud,
      title: '云端集成',
      description: '构建弹性可扩展的云端架构，支持混合云和多云部署策略',
      features: ['云端部署', '弹性扩展', '数据备份', '安全防护'],
      color: 'from-orange-500 to-red-600'
    },
    {
      icon: BarChart3,
      title: '数字化看板',
      description: '实时展示关键业务指标，提供直观的数据可视化和决策支持',
      features: ['实时监控', 'KPI展示', '报表生成', '移动端支持'],
      color: 'from-blue-500 to-indigo-600'
    },
    {
      icon: Shield,
      title: '网络安全',
      description: '全方位的工业网络安全解决方案，保护关键基础设施和数据安全',
      features: ['安全评估', '威胁检测', '访问控制', '合规管理'],
      color: 'from-red-500 to-pink-600'
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1
    }
  }

  return (
    <section id="services" className="py-20 bg-slate-900/50">
      <div className="container mx-auto px-6">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold cyber-font mb-6"
          >
            <span className="bg-gradient-to-r from-cyan-400 to-blue-600 bg-clip-text text-transparent">
              核心服务
            </span>
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            提供全方位的工业4.0数字化解决方案，助力企业实现智能制造转型升级
          </motion.p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ y: -10, scale: 1.02 }}
              className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-8 border border-slate-700/50 hover:border-cyan-500/50 transition-all duration-300 cyber-border group"
            >
              <div className={`w-16 h-16 bg-gradient-to-br ${service.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <service.icon className="w-8 h-8 text-white" />
              </div>
              
              <h3 className="text-2xl font-bold text-white mb-4 cyber-font">
                {service.title}
              </h3>
              
              <p className="text-gray-300 mb-6 leading-relaxed">
                {service.description}
              </p>
              
              <ul className="space-y-2 mb-6">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-gray-400">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full mr-3"></div>
                    {feature}
                  </li>
                ))}
              </ul>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center text-cyan-400 hover:text-cyan-300 transition-colors duration-300 group"
              >
                <span>了解更多</span>
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </motion.button>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-cyan-500/10 to-blue-600/10 rounded-2xl p-8 border border-cyan-500/20">
            <h3 className="text-3xl font-bold text-white mb-4 cyber-font">
              准备开始您的数字化转型之旅？
            </h3>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              我们的专家团队将为您提供定制化的解决方案，助力您的企业在工业4.0时代保持竞争优势
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-cyan-400 hover:to-blue-500 transition-all duration-300 cyber-border animate-glow"
            >
              免费咨询方案
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Services
