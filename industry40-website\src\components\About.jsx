import React from 'react'
import { motion } from 'framer-motion'
import { 
  Award, 
  Users, 
  Target, 
  Lightbulb,
  CheckCircle,
  TrendingUp
} from 'lucide-react'

const About = () => {
  const achievements = [
    { number: '500+', label: '成功项目', icon: Award },
    { number: '50+', label: '专业团队', icon: Users },
    { number: '98%', label: '客户满意度', icon: Target },
    { number: '10年', label: '行业经验', icon: TrendingUp }
  ]

  const values = [
    {
      icon: Lightbulb,
      title: '创新驱动',
      description: '持续投入研发，引领技术创新，为客户提供最前沿的解决方案'
    },
    {
      icon: Users,
      title: '客户至上',
      description: '以客户需求为中心，提供个性化定制服务，确保项目成功交付'
    },
    {
      icon: CheckCircle,
      title: '品质保证',
      description: '严格的质量管控体系，确保每个项目都达到最高标准'
    },
    {
      icon: Target,
      title: '精准执行',
      description: '专业的项目管理团队，确保项目按时按质完成交付'
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1
    }
  }

  return (
    <section id="about" className="py-20 bg-slate-800/30">
      <div className="container mx-auto px-6">
        {/* Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold cyber-font mb-6"
          >
            <span className="bg-gradient-to-r from-cyan-400 to-blue-600 bg-clip-text text-transparent">
              关于我们
            </span>
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            专注工业4.0数字化转型，致力于为制造业企业提供全方位的智能化解决方案
          </motion.p>
        </motion.div>

        {/* Company Introduction */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20"
        >
          <motion.div variants={itemVariants}>
            <h3 className="text-3xl font-bold text-white mb-6 cyber-font">
              引领制造业数字化革命
            </h3>
            <div className="space-y-4 text-gray-300 leading-relaxed">
              <p>
                我们是一家专注于工业4.0数字化转型的科技公司，拥有10年的行业经验和深厚的技术积累。
                我们的使命是通过先进的数字化技术，帮助制造业企业实现智能化转型升级。
              </p>
              <p>
                从智能制造系统到工业物联网，从大数据分析到人工智能应用，我们提供端到端的解决方案，
                助力企业在数字化时代保持竞争优势，实现可持续发展。
              </p>
              <p>
                我们的专业团队由资深工程师、数据科学家和行业专家组成，具备丰富的项目实施经验，
                能够为不同规模和类型的企业提供定制化的数字化转型方案。
              </p>
            </div>
          </motion.div>

          <motion.div variants={itemVariants} className="relative">
            <div className="bg-gradient-to-br from-cyan-500/20 to-blue-600/20 rounded-2xl p-8 cyber-border">
              <div className="grid grid-cols-2 gap-6">
                {achievements.map((achievement, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.05 }}
                    className="text-center"
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <achievement.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-3xl font-bold text-cyan-400 cyber-font mb-2">
                      {achievement.number}
                    </div>
                    <div className="text-gray-300 text-sm">
                      {achievement.label}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Core Values */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.h3
            variants={itemVariants}
            className="text-3xl font-bold text-center text-white mb-12 cyber-font"
          >
            核心价值观
          </motion.h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ y: -10 }}
                className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:border-cyan-500/50 transition-all duration-300 text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <value.icon className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-white mb-3 cyber-font">
                  {value.title}
                </h4>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl p-8 border border-cyan-500/20">
            <h3 className="text-3xl font-bold text-white mb-4 cyber-font">
              与我们一起开启数字化未来
            </h3>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              选择我们作为您的数字化转型伙伴，让我们共同打造智能制造的美好未来
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-cyan-400 hover:to-blue-500 transition-all duration-300 cyber-border"
            >
              了解更多
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
